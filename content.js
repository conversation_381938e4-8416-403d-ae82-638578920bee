let cachedSubtitles = null;

// 预加载字幕 - 添加防重复机制
let isPreloading = false;
function preloadSubtitles() {
    if (isPreloading || cachedSubtitles) {
        console.log('跳过预加载：正在进行中或已有缓存');
        return;
    }

    isPreloading = true;
    getSubtitlesSimple().then(result => {
        cachedSubtitles = result;
        console.log('字幕预加载成功');
    }).catch(error => {
        console.error('预加载字幕失败:', error);
        // 清除缓存，强制重新获取
        cachedSubtitles = null;
    }).finally(() => {
        isPreloading = false;
    });
}

// 备用字幕获取方法
async function getSubtitlesFromDOM() {
    try {
        // 尝试从页面DOM中获取字幕按钮
        const captionButton = document.querySelector('.ytp-subtitles-button, .ytp-caption-window-container');
        if (captionButton) {
            console.log('找到字幕相关元素');
        }

        // 尝试从ytplayer获取
        if (window.ytplayer && window.ytplayer.config) {
            const config = window.ytplayer.config;
            if (config.args && config.args.player_response) {
                const playerResponse = JSON.parse(config.args.player_response);
                return playerResponse;
            }
        }

        return null;
    } catch (error) {
        console.error('DOM方法获取失败:', error);
        return null;
    }
}

// 从页面提取字幕文本的备用方法
async function extractSubtitlesFromPage() {
    try {
        console.log('尝试从页面提取字幕');

        // 方法1: 尝试激活字幕并获取内容
        const result = await activateAndExtractSubtitles();
        if (result) {
            return result;
        }

        // 方法2: 从页面数据中提取
        const pageData = await extractFromPageData();
        if (pageData) {
            return pageData;
        }

        // 方法3: 使用YouTube内部API
        const apiData = await extractViaInternalAPI();
        if (apiData) {
            return apiData;
        }

        // 方法4: 提示用户手动操作
        return '字幕获取失败。请尝试：\n1. 确保视频有字幕（点击CC按钮）\n2. 刷新页面后重试\n3. 尝试其他有字幕的视频';

    } catch (error) {
        console.error('从页面提取字幕失败:', error);
        return null;
    }
}

// 激活字幕并提取内容
async function activateAndExtractSubtitles() {
    try {
        // 查找字幕按钮
        const captionButton = document.querySelector('.ytp-subtitles-button[aria-pressed="false"]');
        if (captionButton) {
            console.log('尝试激活字幕');
            captionButton.click();

            // 等待字幕加载
            await new Promise(resolve => setTimeout(resolve, 2000));

            // 查找字幕内容
            const captionWindow = document.querySelector('.ytp-caption-window-container');
            if (captionWindow) {
                // 模拟播放来获取字幕
                const video = document.querySelector('video');
                if (video && video.paused) {
                    video.play();
                    await new Promise(resolve => setTimeout(resolve, 3000));
                    video.pause();
                }

                // 收集字幕文本
                const captionTexts = [];
                const observer = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        if (mutation.type === 'childList') {
                            const captionSpans = captionWindow.querySelectorAll('.ytp-caption-segment');
                            captionSpans.forEach(span => {
                                const text = span.textContent.trim();
                                if (text && !captionTexts.includes(text)) {
                                    captionTexts.push(text);
                                }
                            });
                        }
                    });
                });

                observer.observe(captionWindow, { childList: true, subtree: true });

                // 等待收集字幕
                await new Promise(resolve => setTimeout(resolve, 5000));
                observer.disconnect();

                if (captionTexts.length > 0) {
                    console.log('通过激活字幕获取到内容:', captionTexts.length, '条');
                    return captionTexts.join('\n');
                }
            }
        }
        return null;
    } catch (error) {
        console.error('激活字幕失败:', error);
        return null;
    }
}

// 从页面数据中提取字幕
async function extractFromPageData() {
    try {
        // 尝试从window对象中获取字幕数据
        if (window.ytInitialPlayerResponse && window.ytInitialPlayerResponse.captions) {
            const captions = window.ytInitialPlayerResponse.captions;
            const tracks = captions.playerCaptionsTracklistRenderer?.captionTracks;

            if (tracks && tracks.length > 0) {
                // 尝试直接构造一个简化的字幕URL
                const track = tracks[0];
                const simpleUrl = `https://www.youtube.com/api/timedtext?v=${getYouTubeVideoId()}&lang=${track.languageCode}&fmt=srv3`;

                console.log('尝试简化URL:', simpleUrl);

                try {
                    const response = await fetch(simpleUrl);
                    const text = await response.text();
                    if (text && text.length > 0 && !text.includes('<html')) {
                        return parseSubtitleText(text);
                    }
                } catch (e) {
                    console.log('简化URL失败:', e.message);
                }
            }
        }
        return null;
    } catch (error) {
        console.error('从页面数据提取失败:', error);
        return null;
    }
}

// 使用YouTube内部API
async function extractViaInternalAPI() {
    try {
        const videoId = getYouTubeVideoId();
        if (!videoId) return null;

        // 尝试使用不同的API端点
        const apiUrls = [
            `https://www.youtube.com/api/timedtext?v=${videoId}&lang=en&fmt=srv3`,
            `https://www.youtube.com/api/timedtext?v=${videoId}&lang=en&fmt=vtt`,
            `https://www.youtube.com/api/timedtext?v=${videoId}&lang=en&fmt=srv1`
        ];

        for (const url of apiUrls) {
            try {
                console.log('尝试API URL:', url);
                const response = await fetch(url);
                const text = await response.text();

                if (text && text.length > 0 && !text.includes('<html')) {
                    console.log('API URL成功:', url);
                    return parseSubtitleText(text);
                }
            } catch (e) {
                console.log('API URL失败:', url, e.message);
            }
        }

        return null;
    } catch (error) {
        console.error('内部API提取失败:', error);
        return null;
    }
}

// 解析字幕文本
function parseSubtitleText(text) {
    try {
        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(text, "text/xml");

        const textElements = xmlDoc.getElementsByTagName('text');
        if (textElements.length > 0) {
            const subtitles = Array.from(textElements).map(element => {
                return element.textContent || element.innerText || '';
            }).filter(text => text.trim().length > 0);

            return subtitles.join('\n');
        }

        // 如果不是XML，可能是VTT格式
        if (text.includes('WEBVTT')) {
            const lines = text.split('\n');
            const subtitles = lines.filter(line => {
                return line.trim() &&
                       !line.includes('WEBVTT') &&
                       !line.includes('-->') &&
                       !line.match(/^\d+$/);
            });
            return subtitles.join('\n');
        }

        return text;
    } catch (error) {
        console.error('解析字幕文本失败:', error);
        return text;
    }
}

// 通过background script获取字幕（绕过CORS）- 添加重试机制
async function fetchSubtitlesViaBackground(url) {
    return new Promise((resolve, reject) => {
        // 添加超时处理
        const timeout = setTimeout(() => {
            reject(new Error('Background script请求超时'));
        }, 10000); // 10秒超时

        chrome.runtime.sendMessage({
            action: 'fetchSubtitles',
            url: url
        }, (response) => {
            clearTimeout(timeout);

            if (chrome.runtime.lastError) {
                reject(new Error(`Chrome runtime error: ${chrome.runtime.lastError.message}`));
            } else if (response && response.error) {
                reject(new Error(`Background error: ${response.error}`));
            } else if (response && response.data) {
                resolve(response.data);
            } else {
                reject(new Error('Background script返回空数据'));
            }
        });
    });
}

// 添加重试机制的background获取函数
async function fetchSubtitlesViaBackgroundWithRetry(url, maxRetries = 2) {
    for (let i = 0; i < maxRetries; i++) {
        try {
            console.log(`Background获取尝试 ${i + 1}/${maxRetries}:`, url.substring(0, 100) + '...');
            const result = await fetchSubtitlesViaBackground(url);
            console.log(`✅ Background获取成功，尝试次数: ${i + 1}`);
            return result;
        } catch (error) {
            console.log(`❌ Background获取失败 ${i + 1}/${maxRetries}:`, error.message);
            if (i === maxRetries - 1) {
                throw error;
            }
            // 等待一段时间后重试
            await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
        }
    }
}

// 在视频加载完成后预加载字幕
window.addEventListener('load', preloadSubtitles);

// 监听页面变化，清理缓存
let currentVideoId = null;
function checkVideoChange() {
    const newVideoId = getYouTubeVideoId();
    if (newVideoId && newVideoId !== currentVideoId) {
        console.log('🔄 检测到视频切换，清理缓存');
        currentVideoId = newVideoId;
        cachedSubtitles = null;
        isPreloading = false;
        isProcessingRequest = false;

        // 延迟预加载新视频的字幕
        setTimeout(preloadSubtitles, 2000);
    }
}

// 监听URL变化（YouTube单页应用）
let lastUrl = location.href;
new MutationObserver(() => {
    const url = location.href;
    if (url !== lastUrl) {
        lastUrl = url;
        setTimeout(checkVideoChange, 1000); // 延迟检查，等待页面稳定
    }
}).observe(document, { subtree: true, childList: true });

// 添加请求锁，防止重复请求
let isProcessingRequest = false;

chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === "getSubtitles") {
        // 如果已有缓存，直接返回
        if (cachedSubtitles) {
            console.log('✅ 使用缓存的字幕数据');
            sendResponse(cachedSubtitles);
            return true;
        }

        // 如果正在处理请求，告知等待
        if (isProcessingRequest) {
            console.log('⏳ 已有请求正在处理中，请稍后');
            sendResponse({error: '正在获取字幕，请稍后再试'});
            return true;
        }

        // 设置锁并处理请求
        isProcessingRequest = true;
        console.log('🚀 开始处理字幕请求');

        // 添加超时处理
        const timeout = setTimeout(() => {
            isProcessingRequest = false;
            sendResponse({error: '字幕获取超时，请重试'});
        }, 30000); // 30秒超时

        getSubtitlesSimple().then(function(result) {
            clearTimeout(timeout);
            if (result && result.subtitles) {
                cachedSubtitles = result;
                sendResponse(result);
                console.log('✅ 字幕获取成功');
            } else {
                sendResponse({error: '未能获取到字幕内容，请确保视频有可用字幕'});
            }
        }).catch(function(error) {
            clearTimeout(timeout);
            console.error('❌ 字幕获取失败:', error);

            // 提供更友好的错误信息
            let errorMessage = error.message;
            if (errorMessage.includes('CORS')) {
                errorMessage = '网络访问受限，请刷新页面后重试';
            } else if (errorMessage.includes('timeout') || errorMessage.includes('超时')) {
                errorMessage = '请求超时，请检查网络连接后重试';
            } else if (errorMessage.includes('404') || errorMessage.includes('not found')) {
                errorMessage = '该视频没有可用字幕';
            } else if (errorMessage.includes('视频ID')) {
                errorMessage = '无法识别当前视频，请确保在YouTube视频页面';
            } else {
                errorMessage = `字幕获取失败: ${errorMessage}`;
            }

            sendResponse({error: errorMessage});
        }).finally(function() {
            isProcessingRequest = false;
        });

        return true; // 保持消息通道开放以进行异步响应
    }
});

async function getYouTubeSubtitles() {
    try {
        const videoId = getYouTubeVideoId();
        if (!videoId) {
            throw new Error('无法获取 YouTube 视频 ID');
        }

        console.log('获取视频ID:', videoId);

        let playerResponse;
        try {
            playerResponse = await getPlayerResponse(videoId);
            console.log('PlayerResponse获取成功');
        } catch (error) {
            console.log('主要方法失败，尝试备用方法');
            playerResponse = await getSubtitlesFromDOM();
            if (!playerResponse) {
                throw new Error('所有获取方法都失败了');
            }
        }

        // 更详细的字幕轨道检查
        const captions = playerResponse.captions;
        console.log('字幕数据:', captions);

        if (!captions) {
            throw new Error('该视频没有字幕数据');
        }

        const captionTracks = captions.playerCaptionsTracklistRenderer?.captionTracks;
        console.log('字幕轨道:', captionTracks);

        if (!captionTracks || captionTracks.length === 0) {
            throw new Error('该视频没有可用字幕');
        }

        // 优先级：中文 > 英文 > 自动生成 > 其他语言
        let track = captionTracks.find(track => track.languageCode === 'zh-CN' || track.languageCode === 'zh') ||
                   captionTracks.find(track => track.languageCode === 'en') ||
                   captionTracks.find(track => track.kind === 'asr') ||
                   captionTracks[0]; // 如果都没有，使用第一个可用的

        if (!track) {
            throw new Error('没有可用的字幕');
        }

        console.log('选择的字幕轨道:', track);

        let subtitles;
        try {
            subtitles = await fetchSubtitles(track.baseUrl);
        } catch (error) {
            console.log('主要字幕获取方法失败，尝试备用方法');

            // 备用方法1：尝试使用background script获取
            try {
                subtitles = await fetchSubtitlesViaBackground(track.baseUrl);
            } catch (bgError) {
                console.log('Background方法失败，尝试不同的URL格式');

                // 备用方法2：尝试使用不同的URL格式
                const alternativeUrl = track.baseUrl.replace(/&fmt=srv3/, '').replace(/&fmt=vtt/, '');
                try {
                    subtitles = await fetchSubtitles(alternativeUrl);
                } catch (altError) {
                    console.log('备用URL也失败，尝试从页面提取');
                    subtitles = await extractSubtitlesFromPage();
                    if (!subtitles) {
                        throw error; // 抛出原始错误
                    }
                }
            }
        }

        return {
            language: track.languageCode === 'zh-CN' || track.languageCode === 'zh' ? '中文' :
                      track.languageCode === 'en' ? '英文' :
                      track.kind === 'asr' ? '自动生成' :
                      track.name?.simpleText || '未知语言',
            subtitles: subtitles
        };
    } catch (error) {
        console.error('getYouTubeSubtitles错误:', error);
        throw error;
    }
}

async function getPlayerResponse(videoId) {
    try {
        // 方法1: 尝试从当前页面的window对象获取
        if (window.ytInitialPlayerResponse) {
            console.log('使用window.ytInitialPlayerResponse');
            return window.ytInitialPlayerResponse;
        }

        // 方法2: 尝试从页面HTML中提取
        const response = await fetch(`https://www.youtube.com/watch?v=${videoId}`);
        const html = await response.text();

        // 尝试多种正则表达式模式
        const patterns = [
            /ytInitialPlayerResponse\s*=\s*({.+?});/,
            /var\s+ytInitialPlayerResponse\s*=\s*({.+?});/,
            /"ytInitialPlayerResponse":({.+?}),"ytInitialData"/,
            /ytInitialPlayerResponse":\s*({.+?}),"ytInitialData"/
        ];

        for (const pattern of patterns) {
            const match = html.match(pattern);
            if (match) {
                console.log('使用HTML提取方法，模式:', pattern);
                return JSON.parse(match[1]);
            }
        }

        // 方法3: 尝试使用YouTube内部API
        const apiResponse = await fetch(`https://www.youtube.com/youtubei/v1/player?key=AIzaSyAO_FJ2SlqU8Q4STEHLGCilw_Y9_11qcW8`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                context: {
                    client: {
                        clientName: 'WEB',
                        clientVersion: '2.20231201.01.00'
                    }
                },
                videoId: videoId
            })
        });

        if (apiResponse.ok) {
            console.log('使用YouTube API方法');
            return await apiResponse.json();
        }

        throw new Error('无法获取视频信息 - 所有方法都失败了');
    } catch (error) {
        console.error('getPlayerResponse错误:', error);
        throw new Error(`无法获取视频信息: ${error.message}`);
    }
}

async function fetchSubtitles(url) {
    try {
        console.log('获取字幕URL:', url);

        // 确保URL是完整的
        const fullUrl = url.startsWith('http') ? url : `https://www.youtube.com${url}`;

        // 尝试多种请求方式
        let response;

        // 方法1: 简单请求
        try {
            response = await fetch(fullUrl);
        } catch (error) {
            console.log('简单请求失败，尝试带头部的请求');

            // 方法2: 带请求头的请求
            response = await fetch(fullUrl, {
                method: 'GET',
                headers: {
                    'Accept': 'text/xml, application/xml, text/plain, */*',
                    'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache'
                },
                mode: 'cors',
                credentials: 'omit'
            });
        }

        if (!response.ok) {
            throw new Error(`字幕请求失败: ${response.status} ${response.statusText}`);
        }

        const subtitlesXml = await response.text();
        console.log('字幕XML长度:', subtitlesXml.length);
        console.log('字幕XML前100字符:', subtitlesXml.substring(0, 100));

        if (subtitlesXml.length === 0) {
            throw new Error('字幕服务器返回空内容');
        }

        // 检查是否返回了错误页面而不是XML
        if (subtitlesXml.includes('<html') || subtitlesXml.includes('<!DOCTYPE')) {
            console.log('返回的是HTML页面，不是XML字幕');
            throw new Error('字幕服务器返回了错误页面');
        }

        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(subtitlesXml, "text/xml");

        // 检查XML解析错误
        const parseError = xmlDoc.querySelector('parsererror');
        if (parseError) {
            console.log('XML解析错误:', parseError.textContent);
            throw new Error('字幕XML解析失败');
        }

        const textElements = xmlDoc.getElementsByTagName('text');
        console.log('找到字幕条目数:', textElements.length);

        if (textElements.length === 0) {
            // 尝试其他可能的标签名
            const altElements = xmlDoc.getElementsByTagName('p') ||
                              xmlDoc.getElementsByTagName('span') ||
                              xmlDoc.getElementsByTagName('s');
            if (altElements.length > 0) {
                console.log('使用备用标签，找到条目数:', altElements.length);
                const subtitles = Array.from(altElements).map(element => {
                    const text = element.textContent || element.innerText || '';
                    return text.replace(/&amp;/g, '&')
                              .replace(/&lt;/g, '<')
                              .replace(/&gt;/g, '>')
                              .replace(/&quot;/g, '"')
                              .replace(/&#39;/g, "'");
                }).filter(text => text.trim().length > 0);

                return subtitles.join('\n');
            }

            throw new Error('字幕文件中没有找到文本内容');
        }

        const subtitles = Array.from(textElements).map(element => {
            // 解码HTML实体
            const text = element.textContent || element.innerText || '';
            return text.replace(/&amp;/g, '&')
                      .replace(/&lt;/g, '<')
                      .replace(/&gt;/g, '>')
                      .replace(/&quot;/g, '"')
                      .replace(/&#39;/g, "'");
        }).filter(text => text.trim().length > 0);

        return subtitles.join('\n');
    } catch (error) {
        console.error('fetchSubtitles错误:', error);
        throw new Error(`获取字幕失败: ${error.message}`);
    }
}

function getYouTubeVideoId() {
    try {
        const url = window.location.href;
        console.log('当前URL:', url);

        // 方法1: 从URL参数获取
        const urlParams = new URLSearchParams(window.location.search);
        const videoId = urlParams.get('v');
        if (videoId) {
            console.log('从URL参数获取视频ID:', videoId);
            return videoId;
        }

        // 方法2: 从路径中提取（适用于 /watch?v= 和 /embed/ 格式）
        const pathMatch = url.match(/(?:watch\?v=|embed\/|v\/|youtu\.be\/)([a-zA-Z0-9_-]{11})/);
        if (pathMatch) {
            console.log('从路径提取视频ID:', pathMatch[1]);
            return pathMatch[1];
        }

        // 方法3: 从页面元素获取
        const canonicalLink = document.querySelector('link[rel="canonical"]');
        if (canonicalLink) {
            const canonicalMatch = canonicalLink.href.match(/watch\?v=([a-zA-Z0-9_-]{11})/);
            if (canonicalMatch) {
                console.log('从canonical链接获取视频ID:', canonicalMatch[1]);
                return canonicalMatch[1];
            }
        }

        // 方法4: 从meta标签获取
        const metaTag = document.querySelector('meta[property="og:url"]');
        if (metaTag) {
            const metaMatch = metaTag.content.match(/watch\?v=([a-zA-Z0-9_-]{11})/);
            if (metaMatch) {
                console.log('从meta标签获取视频ID:', metaMatch[1]);
                return metaMatch[1];
            }
        }

        console.error('无法从任何方法获取视频ID');
        return null;
    } catch (error) {
        console.error('getYouTubeVideoId错误:', error);
        return null;
    }
}

// 简化的字幕获取主函数 - 优化执行顺序
async function getSubtitlesSimple() {
    try {
        console.log('🚀 开始简化字幕获取流程');

        // 1. 获取视频ID
        const videoId = getYouTubeVideoId();
        if (!videoId) {
            throw new Error('无法获取视频ID');
        }
        console.log('📹 视频ID:', videoId);

        // 2. 优先尝试最可靠的方法：简化API调用
        console.log('🔄 方法1: 尝试简化API调用');
        const apiSubtitles = await getSubtitlesFromSimpleAPI(videoId);
        if (apiSubtitles) {
            console.log('✅ API调用成功');
            return {
                language: 'API获取',
                subtitles: apiSubtitles
            };
        }

        // 3. 备用方法：从页面脚本提取
        console.log('🔄 方法2: 尝试从页面脚本提取');
        const directSubtitles = await getSubtitlesFromPageScript();
        if (directSubtitles) {
            console.log('✅ 页面脚本提取成功');
            return {
                language: '页面提取',
                subtitles: directSubtitles
            };
        }

        // 4. 最后尝试：激活字幕实时获取（可能影响用户体验）
        console.log('🔄 方法3: 尝试实时获取（可能会播放视频）');
        const liveSubtitles = await getLiveSubtitlesSimple();
        if (liveSubtitles) {
            console.log('✅ 实时获取成功');
            return {
                language: '实时获取',
                subtitles: liveSubtitles
            };
        }

        throw new Error('❌ 所有方法都无法获取字幕，请确保视频有可用字幕');

    } catch (error) {
        console.error('❌ 简化字幕获取失败:', error);
        throw error;
    }
}

// 从页面脚本中直接提取字幕URL - 优化搜索效率，使用background script
async function getSubtitlesFromPageScript() {
    try {
        console.log('🔍 尝试从页面脚本提取字幕');

        // 方法1：尝试从window对象直接获取
        if (window.ytInitialPlayerResponse && window.ytInitialPlayerResponse.captions) {
            console.log('✅ 从window.ytInitialPlayerResponse获取字幕数据');
            const captions = window.ytInitialPlayerResponse.captions;
            const tracks = captions.playerCaptionsTracklistRenderer?.captionTracks;

            if (tracks && tracks.length > 0) {
                for (const track of tracks) {
                    if (track.baseUrl) {
                        console.log('🔗 尝试window对象中的字幕URL:', track.baseUrl.substring(0, 100) + '...');

                        try {
                            // 优先使用background script with retry
                            let text;
                            try {
                                text = await fetchSubtitlesViaBackgroundWithRetry(track.baseUrl);
                                console.log('✅ Background方式获取成功');
                            } catch (bgError) {
                                console.log('❌ Background方式失败，尝试直接fetch');
                                const response = await fetch(track.baseUrl, {
                                    signal: AbortSignal.timeout(3000)
                                });
                                if (response.ok) {
                                    text = await response.text();
                                }
                            }

                            if (text && text.length > 0 && !text.includes('<html') && !text.includes('404')) {
                                console.log('✅ window对象字幕获取成功');
                                return parseSubtitleContent(text);
                            }
                        } catch (e) {
                            console.log('❌ window对象字幕URL失败:', e.message);
                        }
                    }
                }
            }
        }

        // 方法2：从页面脚本中搜索
        const scripts = document.querySelectorAll('script');
        let foundScript = null;

        // 快速筛选包含字幕数据的脚本
        for (const script of scripts) {
            const content = script.textContent || script.innerHTML;
            if (content.includes('captionTracks') && content.includes('baseUrl')) {
                foundScript = content;
                console.log('✅ 找到包含字幕数据的脚本');
                break; // 找到第一个就退出
            }
        }

        if (!foundScript) {
            console.log('❌ 未找到包含字幕数据的脚本');
            return null;
        }

        // 提取所有可能的字幕URL
        const urlMatches = foundScript.match(/"baseUrl":"([^"]*timedtext[^"]*)"/g);
        if (!urlMatches) {
            console.log('❌ 未找到字幕URL');
            return null;
        }

        console.log(`🔗 找到 ${urlMatches.length} 个字幕URL`);

        // 尝试前2个URL
        for (let i = 0; i < Math.min(urlMatches.length, 2); i++) {
            const urlMatch = urlMatches[i].match(/"baseUrl":"([^"]*)"/);
            if (urlMatch) {
                let url = urlMatch[1].replace(/\\u0026/g, '&').replace(/\\/g, '');
                console.log(`🔗 尝试字幕URL ${i + 1}:`, url.substring(0, 100) + '...');

                try {
                    // 优先使用background script with retry
                    let text;
                    try {
                        text = await fetchSubtitlesViaBackgroundWithRetry(url);
                        console.log(`✅ Background方式成功 URL ${i + 1}`);
                    } catch (bgError) {
                        console.log(`❌ Background方式失败 URL ${i + 1}，尝试直接fetch`);
                        const response = await fetch(url, {
                            signal: AbortSignal.timeout(3000)
                        });
                        if (response.ok) {
                            text = await response.text();
                            console.log(`✅ 直接fetch成功 URL ${i + 1}`);
                        }
                    }

                    if (text && text.length > 0 && !text.includes('<html') && !text.includes('404')) {
                        console.log('✅ 页面脚本提取成功');
                        return parseSubtitleContent(text);
                    }
                } catch (e) {
                    console.log(`❌ 字幕URL ${i + 1} 请求失败:`, e.message);
                }
            }
        }

        return null;
    } catch (error) {
        console.error('❌ 页面脚本提取失败:', error);
        return null;
    }
}

// 简化的API调用 - 优化请求顺序和错误处理，使用background script绕过CORS
async function getSubtitlesFromSimpleAPI(videoId) {
    try {
        console.log('尝试简化API调用（通过background script）');

        // 优化：按成功率排序，减少无效请求
        const apiConfigs = [
            { lang: 'en', fmt: 'srv3' },
            { lang: 'en', fmt: 'vtt' },
            { lang: 'zh-CN', fmt: 'srv3' },
            { lang: 'zh', fmt: 'srv3' },
            { lang: 'en', fmt: 'srv1' }
        ];

        for (const config of apiConfigs) {
            const url = `https://www.youtube.com/api/timedtext?v=${videoId}&lang=${config.lang}&fmt=${config.fmt}`;

            try {
                console.log(`尝试 ${config.lang}/${config.fmt}:`, url);

                // 优先使用background script绕过CORS
                let text;
                try {
                    text = await fetchSubtitlesViaBackgroundWithRetry(url);
                    console.log(`✅ Background方式成功: ${config.lang}/${config.fmt}`);
                } catch (bgError) {
                    console.log(`❌ Background方式失败: ${bgError.message}`);

                    // 备用：直接fetch（可能被CORS阻止）
                    try {
                        const response = await fetch(url, {
                            method: 'GET',
                            mode: 'cors',
                            credentials: 'omit',
                            signal: AbortSignal.timeout(3000) // 减少超时时间
                        });

                        if (response.ok) {
                            text = await response.text();
                            console.log(`✅ 直接fetch成功: ${config.lang}/${config.fmt}`);
                        } else {
                            console.log(`❌ ${config.lang}/${config.fmt}: HTTP ${response.status}`);
                            continue;
                        }
                    } catch (fetchError) {
                        console.log(`❌ ${config.lang}/${config.fmt}: ${fetchError.message}`);
                        continue;
                    }
                }

                if (text && text.length > 0 && !text.includes('<html') && !text.includes('404') && !text.includes('error')) {
                    console.log('✅ 简化API成功:', config);
                    return parseSubtitleContent(text);
                } else {
                    console.log(`❌ ${config.lang}/${config.fmt}: 空内容或错误页面`);
                }
            } catch (e) {
                if (e.name === 'AbortError') {
                    console.log(`⏰ ${config.lang}/${config.fmt}: 请求超时`);
                } else {
                    console.log(`❌ ${config.lang}/${config.fmt}: ${e.message}`);
                }
            }
        }

        console.log('所有API调用都失败');
        return null;
    } catch (error) {
        console.error('简化API调用失败:', error);
        return null;
    }
}

// 简化的实时字幕获取 - 减少对用户体验的影响，添加更多检测方法
async function getLiveSubtitlesSimple() {
    try {
        console.log('🎬 尝试实时获取字幕（最后手段）');

        // 方法1：检查是否已有字幕显示在页面上
        const existingCaptions = document.querySelectorAll('.ytp-caption-segment, .caption-window span, .captions-text span');
        if (existingCaptions.length > 0) {
            console.log('✅ 发现页面上已有字幕');
            const texts = Array.from(existingCaptions).map(span => span.textContent.trim()).filter(text => text);
            if (texts.length > 0) {
                return texts.join('\n');
            }
        }

        // 方法2：尝试从字幕轨道获取
        const video = document.querySelector('video');
        if (video && video.textTracks && video.textTracks.length > 0) {
            console.log('🔍 检查video textTracks');
            for (const track of video.textTracks) {
                if (track.kind === 'subtitles' || track.kind === 'captions') {
                    if (track.cues && track.cues.length > 0) {
                        console.log('✅ 从textTracks获取字幕');
                        const cueTexts = Array.from(track.cues).map(cue => cue.text).filter(text => text.trim());
                        if (cueTexts.length > 0) {
                            return cueTexts.join('\n');
                        }
                    }
                }
            }
        }

        // 方法3：查找并激活字幕按钮
        const captionButton = document.querySelector('.ytp-subtitles-button, .ytp-caption-window-button');
        if (!captionButton) {
            console.log('❌ 未找到字幕按钮');
            return null;
        }

        const isActive = captionButton.getAttribute('aria-pressed') === 'true';
        const wasPlaying = video && !video.paused;

        // 如果字幕未激活，先激活
        if (!isActive) {
            console.log('🔄 激活字幕');
            captionButton.click();
            await new Promise(resolve => setTimeout(resolve, 800));
        }

        // 查找字幕容器
        const captionContainer = document.querySelector('.ytp-caption-window-container, .caption-window');
        if (!captionContainer) {
            console.log('❌ 未找到字幕容器');
            return null;
        }

        // 再次检查是否有字幕显示
        const newSubtitles = captionContainer.querySelectorAll('.ytp-caption-segment');
        if (newSubtitles.length > 0) {
            console.log('✅ 激活后发现字幕');
            const texts = Array.from(newSubtitles).map(span => span.textContent.trim()).filter(text => text);
            if (texts.length > 0) {
                return texts.join('\n');
            }
        }

        // 方法4：如果仍然没有字幕，尝试短暂播放获取
        console.log('🎮 尝试短暂播放获取字幕');
        const subtitles = [];
        let collectionStarted = false;

        const collectSubtitles = () => {
            const spans = captionContainer.querySelectorAll('.ytp-caption-segment');
            spans.forEach(span => {
                const text = span.textContent.trim();
                if (text && !subtitles.includes(text)) {
                    subtitles.push(text);
                    collectionStarted = true;
                    console.log('📝 收集到字幕:', text);
                }
            });
        };

        // 监听字幕变化
        const observer = new MutationObserver(collectSubtitles);
        observer.observe(captionContainer, { childList: true, subtree: true });

        // 立即收集一次
        collectSubtitles();

        // 如果视频暂停，短暂播放
        if (video && video.paused) {
            const currentTime = video.currentTime;
            console.log('▶️ 短暂播放视频获取字幕');
            video.play();

            // 等待字幕出现，最多等待2秒
            let waitTime = 0;
            while (waitTime < 2000 && subtitles.length === 0) {
                await new Promise(resolve => setTimeout(resolve, 200));
                waitTime += 200;
                collectSubtitles();
            }

            // 恢复暂停状态
            video.pause();
            video.currentTime = currentTime;
            console.log('⏸️ 恢复视频暂停状态');
        } else {
            // 如果已在播放，等待字幕出现
            let waitTime = 0;
            while (waitTime < 1500 && subtitles.length === 0) {
                await new Promise(resolve => setTimeout(resolve, 200));
                waitTime += 200;
                collectSubtitles();
            }
        }

        observer.disconnect();

        if (subtitles.length > 0) {
            console.log('✅ 实时获取成功，字幕条数:', subtitles.length);
            return subtitles.join('\n');
        }

        console.log('❌ 实时获取未找到字幕');
        return null;
    } catch (error) {
        console.error('❌ 实时获取失败:', error);
        return null;
    }
}

// 解析字幕内容
function parseSubtitleContent(content) {
    try {
        // 尝试XML解析
        if (content.includes('<?xml') || content.includes('<transcript>') || content.includes('<text')) {
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(content, "text/xml");
            const textElements = xmlDoc.getElementsByTagName('text');

            if (textElements.length > 0) {
                const subtitles = Array.from(textElements).map(element => {
                    return (element.textContent || '').trim();
                }).filter(text => text.length > 0);

                return subtitles.join('\n');
            }
        }

        // 尝试VTT解析
        if (content.includes('WEBVTT')) {
            const lines = content.split('\n');
            const subtitles = lines.filter(line => {
                const trimmed = line.trim();
                return trimmed &&
                       !trimmed.includes('WEBVTT') &&
                       !trimmed.includes('-->') &&
                       !trimmed.match(/^\d+$/) &&
                       !trimmed.match(/^\d{2}:\d{2}:\d{2}/);
            });

            return subtitles.join('\n');
        }

        // 如果都不是，直接返回内容
        return content;

    } catch (error) {
        console.error('解析字幕内容失败:', error);
        return content;
    }
}