// 快速测试修复后的字幕功能
// 在YouTube页面控制台运行

console.log('=== SubSnap 快速测试 ===');

async function quickTest() {
    try {
        console.log('1. 测试简化字幕获取...');
        const result = await getSubtitlesSimple();
        
        if (result && result.subtitles) {
            console.log('✅ 字幕获取成功!');
            console.log('语言:', result.language);
            console.log('字幕长度:', result.subtitles.length);
            console.log('前200字符:', result.subtitles.substring(0, 200));
            
            // 测试插件通信
            console.log('\n2. 测试插件通信...');
            chrome.runtime.sendMessage({action: "getSubtitles"}, (response) => {
                if (response && response.subtitles) {
                    console.log('✅ 插件通信成功!');
                    console.log('插件返回语言:', response.language);
                } else {
                    console.log('❌ 插件通信失败:', response?.error);
                }
            });
            
        } else {
            console.log('❌ 字幕获取失败');
        }
        
    } catch (error) {
        console.log('❌ 测试失败:', error.message);
    }
}

// 运行测试
quickTest();

// 也可以手动调用各个方法测试
window.testSubSnap = {
    quickTest,
    getSubtitlesSimple,
    getSubtitlesFromPageScript,
    getSubtitlesFromSimpleAPI,
    getLiveSubtitlesSimple
};
