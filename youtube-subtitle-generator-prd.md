# SubSnap: YouTube字幕快照插件 - 产品需求文档

## 1. 产品概述

SubSnap 是一个 Chrome 浏览器插件，旨在为用户提供快速获取并复制 YouTube 视频字幕的功能。用户只需点击插件图标，即可获取当前正在观看的 YouTube 视频的字幕，支持英文和中文。

## 2. 主要功能

### 2.1 字幕获取
- 用户点击 Chrome 工具栏中的插件图标，触发字幕获取功能
- 插件自动检测当前标签页是否为 YouTube 视频页面
- 如果是 YouTube 视频页面，插件会使用 YouTube 字幕下载库获取可用字幕

### 2.2 语言处理
- 优先获取中文字幕（如果可用）
- 如果没有中文字幕，则获取英文字幕
- 如果既没有中文也没有英文字幕，通知用户当前视频没有可用字幕



### 2.4 复制功能
- 提供"复制全部"按钮，允许用户一键复制所有字幕内容
- 允许用户选择并复制部分字幕内容

## 3. 用户界面

### 3.1 插件图标
- 在 Chrome 工具栏显示一个醒目的插件图标
- 图标尺寸：128x128 像素
- 图标设计应与字幕或 YouTube 主题相关

### 3.2 弹出窗口
- 点击插件图标后显示弹出窗口
- 窗口顶部显示当前获取的字幕语言（中文或英文）
- 中间区域显示获取的字幕内容
- 底部显示"复制全部"按钮

## 4. 技术要求

### 4.1 字幕下载库
- 使用开源的 YouTube 字幕下载 JavaScript 库
- 建议考虑使用如 youtube-captions-scraper 或类似的 GitHub 上维护良好的库

### 4.2 Chrome 扩展开发
- 使用 Manifest V3 开发
- 确保代码结构符合 Manifest V3 的最佳实践

### 4.3 性能
- 字幕获取过程应在 5 秒内完成
- 插件应该轻量化，不影响浏览器整体性能



## 5. 错误处理

- 如果视频没有可用的中文或英文字幕，显示友好的错误消息
- 如果字幕获取失败，提供重试选项和错误反馈机制


