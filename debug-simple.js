// 简化的调试脚本 - 直接在YouTube页面控制台运行
// 用于快速诊断字幕获取问题

console.log('🔧 SubSnap 调试工具启动...');

// 获取视频ID
function getVideoId() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('v');
}

// 测试简单的API调用
async function testSimpleAPI() {
    const videoId = getVideoId();
    if (!videoId) {
        console.log('❌ 无法获取视频ID');
        return;
    }
    
    console.log('📹 视频ID:', videoId);
    
    const testUrls = [
        `https://www.youtube.com/api/timedtext?v=${videoId}&lang=en&fmt=srv3`,
        `https://www.youtube.com/api/timedtext?v=${videoId}&lang=en&fmt=vtt`,
        `https://www.youtube.com/api/timedtext?v=${videoId}&lang=zh-CN&fmt=srv3`
    ];
    
    for (const url of testUrls) {
        try {
            console.log('🔗 测试URL:', url);
            const response = await fetch(url);
            console.log('📊 响应状态:', response.status, response.statusText);
            
            if (response.ok) {
                const text = await response.text();
                console.log('📝 响应长度:', text.length);
                console.log('📄 响应预览:', text.substring(0, 200) + '...');
                
                if (text.includes('<text')) {
                    console.log('✅ 发现XML字幕内容');
                } else if (text.includes('WEBVTT')) {
                    console.log('✅ 发现VTT字幕内容');
                } else {
                    console.log('⚠️ 未识别的内容格式');
                }
            }
        } catch (error) {
            console.log('❌ 请求失败:', error.message);
        }
    }
}

// 检查页面数据
function checkPageData() {
    console.log('\n🔍 检查页面数据...');
    
    if (window.ytInitialPlayerResponse) {
        const captions = window.ytInitialPlayerResponse.captions;
        if (captions && captions.playerCaptionsTracklistRenderer) {
            const tracks = captions.playerCaptionsTracklistRenderer.captionTracks;
            if (tracks && tracks.length > 0) {
                console.log(`✅ 找到 ${tracks.length} 个字幕轨道:`);
                tracks.forEach((track, i) => {
                    console.log(`  ${i + 1}. ${track.languageCode} - ${track.name?.simpleText || '未知'}`);
                    if (track.baseUrl) {
                        console.log(`     URL: ${track.baseUrl.substring(0, 100)}...`);
                    }
                });
                return tracks;
            }
        }
    }
    
    console.log('❌ 未找到页面字幕数据');
    return null;
}

// 检查当前字幕显示
function checkCurrentSubtitles() {
    console.log('\n👀 检查当前字幕显示...');
    
    const subtitleElements = document.querySelectorAll('.ytp-caption-segment');
    if (subtitleElements.length > 0) {
        console.log(`✅ 发现 ${subtitleElements.length} 条显示中的字幕:`);
        Array.from(subtitleElements).forEach((el, i) => {
            console.log(`  ${i + 1}. "${el.textContent.trim()}"`);
        });
        return true;
    }
    
    console.log('ℹ️ 当前无字幕显示');
    return false;
}

// 尝试激活字幕
function tryActivateSubtitles() {
    console.log('\n🎛️ 尝试激活字幕...');
    
    const button = document.querySelector('.ytp-subtitles-button');
    if (button) {
        const isActive = button.getAttribute('aria-pressed') === 'true';
        console.log('字幕按钮状态:', isActive ? '已激活' : '未激活');
        
        if (!isActive) {
            console.log('🔄 点击激活字幕按钮...');
            button.click();
            setTimeout(() => {
                console.log('✅ 字幕激活完成');
                checkCurrentSubtitles();
            }, 1000);
        }
        return true;
    }
    
    console.log('❌ 未找到字幕按钮');
    return false;
}

// 主要诊断函数
async function diagnose() {
    console.log('🚀 开始诊断...\n');
    
    // 1. 检查基础信息
    console.log('1️⃣ 基础信息检查');
    console.log('当前URL:', window.location.href);
    console.log('视频ID:', getVideoId());
    
    // 2. 检查页面数据
    console.log('\n2️⃣ 页面数据检查');
    const pageData = checkPageData();
    
    // 3. 检查当前字幕
    console.log('\n3️⃣ 当前字幕检查');
    const hasCurrentSubs = checkCurrentSubtitles();
    
    // 4. 尝试激活字幕
    if (!hasCurrentSubs) {
        console.log('\n4️⃣ 尝试激活字幕');
        tryActivateSubtitles();
    }
    
    // 5. 测试API调用
    console.log('\n5️⃣ API调用测试');
    await testSimpleAPI();
    
    // 6. 总结
    console.log('\n📋 诊断总结:');
    console.log('- 视频ID:', getVideoId() ? '✅' : '❌');
    console.log('- 页面数据:', pageData ? '✅' : '❌');
    console.log('- 当前字幕:', hasCurrentSubs ? '✅' : '❌');
    
    if (pageData && pageData.length > 0) {
        console.log('\n💡 建议: 页面有字幕数据，尝试使用插件获取字幕');
    } else {
        console.log('\n⚠️ 警告: 页面无字幕数据，该视频可能没有字幕');
    }
}

// 快速测试函数
window.testSubtitles = diagnose;

// 自动运行诊断
diagnose();
