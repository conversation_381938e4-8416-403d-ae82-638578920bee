// 测试字幕获取修复效果
// 在YouTube页面的控制台中运行此脚本来测试

console.log('🧪 开始测试字幕获取修复效果...');

// 测试1: 检查视频ID获取
function testVideoIdExtraction() {
    console.log('\n📹 测试1: 视频ID提取');
    
    try {
        // 模拟getYouTubeVideoId函数
        const url = window.location.href;
        console.log('当前URL:', url);
        
        // 方法1: 从URL参数提取
        const urlParams = new URLSearchParams(window.location.search);
        const videoId = urlParams.get('v');
        
        if (videoId) {
            console.log('✅ 视频ID提取成功:', videoId);
            return videoId;
        } else {
            console.log('❌ 无法从URL提取视频ID');
            return null;
        }
    } catch (error) {
        console.error('❌ 视频ID提取失败:', error);
        return null;
    }
}

// 测试2: 检查页面字幕数据
function testPageSubtitleData() {
    console.log('\n🔍 测试2: 页面字幕数据检查');
    
    try {
        // 检查window对象中的字幕数据
        if (window.ytInitialPlayerResponse) {
            console.log('✅ 找到ytInitialPlayerResponse');
            
            const captions = window.ytInitialPlayerResponse.captions;
            if (captions) {
                console.log('✅ 找到字幕数据');
                const tracks = captions.playerCaptionsTracklistRenderer?.captionTracks;
                
                if (tracks && tracks.length > 0) {
                    console.log(`✅ 找到 ${tracks.length} 个字幕轨道`);
                    tracks.forEach((track, index) => {
                        console.log(`  轨道 ${index + 1}:`, {
                            language: track.languageCode,
                            name: track.name?.simpleText,
                            baseUrl: track.baseUrl ? track.baseUrl.substring(0, 100) + '...' : 'N/A'
                        });
                    });
                    return tracks;
                } else {
                    console.log('❌ 未找到字幕轨道');
                }
            } else {
                console.log('❌ 未找到字幕数据');
            }
        } else {
            console.log('❌ 未找到ytInitialPlayerResponse');
        }
        
        return null;
    } catch (error) {
        console.error('❌ 页面字幕数据检查失败:', error);
        return null;
    }
}

// 测试3: 检查页面脚本中的字幕URL
function testScriptSubtitleUrls() {
    console.log('\n📜 测试3: 页面脚本字幕URL检查');
    
    try {
        const scripts = document.querySelectorAll('script');
        let foundUrls = [];
        
        for (const script of scripts) {
            const content = script.textContent || script.innerHTML;
            if (content.includes('captionTracks') && content.includes('baseUrl')) {
                const urlMatches = content.match(/"baseUrl":"([^"]*timedtext[^"]*)"/g);
                if (urlMatches) {
                    urlMatches.forEach(match => {
                        const urlMatch = match.match(/"baseUrl":"([^"]*)"/);
                        if (urlMatch) {
                            let url = urlMatch[1].replace(/\\u0026/g, '&').replace(/\\/g, '');
                            foundUrls.push(url);
                        }
                    });
                }
            }
        }
        
        if (foundUrls.length > 0) {
            console.log(`✅ 在页面脚本中找到 ${foundUrls.length} 个字幕URL`);
            foundUrls.forEach((url, index) => {
                console.log(`  URL ${index + 1}:`, url.substring(0, 100) + '...');
            });
            return foundUrls;
        } else {
            console.log('❌ 未在页面脚本中找到字幕URL');
            return [];
        }
    } catch (error) {
        console.error('❌ 页面脚本字幕URL检查失败:', error);
        return [];
    }
}

// 测试4: 检查字幕按钮和容器
function testSubtitleElements() {
    console.log('\n🎛️ 测试4: 字幕UI元素检查');
    
    try {
        // 检查字幕按钮
        const captionButton = document.querySelector('.ytp-subtitles-button, .ytp-caption-window-button');
        if (captionButton) {
            console.log('✅ 找到字幕按钮');
            console.log('  按钮状态:', captionButton.getAttribute('aria-pressed'));
        } else {
            console.log('❌ 未找到字幕按钮');
        }
        
        // 检查字幕容器
        const captionContainer = document.querySelector('.ytp-caption-window-container, .caption-window');
        if (captionContainer) {
            console.log('✅ 找到字幕容器');
            
            // 检查当前显示的字幕
            const currentSubtitles = captionContainer.querySelectorAll('.ytp-caption-segment');
            if (currentSubtitles.length > 0) {
                console.log(`✅ 当前显示 ${currentSubtitles.length} 条字幕`);
                Array.from(currentSubtitles).forEach((subtitle, index) => {
                    console.log(`  字幕 ${index + 1}:`, subtitle.textContent.trim());
                });
            } else {
                console.log('ℹ️ 当前无字幕显示');
            }
        } else {
            console.log('❌ 未找到字幕容器');
        }
        
        // 检查video元素的textTracks
        const video = document.querySelector('video');
        if (video && video.textTracks && video.textTracks.length > 0) {
            console.log(`✅ 视频元素有 ${video.textTracks.length} 个文本轨道`);
            for (let i = 0; i < video.textTracks.length; i++) {
                const track = video.textTracks[i];
                console.log(`  轨道 ${i + 1}:`, {
                    kind: track.kind,
                    language: track.language,
                    mode: track.mode,
                    cues: track.cues ? track.cues.length : 0
                });
            }
        } else {
            console.log('ℹ️ 视频元素无文本轨道');
        }
        
    } catch (error) {
        console.error('❌ 字幕UI元素检查失败:', error);
    }
}

// 运行所有测试
async function runAllTests() {
    console.log('🚀 开始运行所有测试...\n');
    
    const videoId = testVideoIdExtraction();
    const pageData = testPageSubtitleData();
    const scriptUrls = testScriptSubtitleUrls();
    testSubtitleElements();
    
    console.log('\n📊 测试总结:');
    console.log('- 视频ID:', videoId ? '✅' : '❌');
    console.log('- 页面字幕数据:', pageData ? '✅' : '❌');
    console.log('- 脚本字幕URL:', scriptUrls.length > 0 ? '✅' : '❌');
    
    if (videoId && (pageData || scriptUrls.length > 0)) {
        console.log('\n🎉 基础数据检查通过，字幕获取应该可以工作！');
    } else {
        console.log('\n⚠️ 基础数据检查未完全通过，可能需要进一步调试');
    }
}

// 自动运行测试
runAllTests();
