let subtitlesData = null;

function getSubtitles() {
    // 显示加载状态
    document.getElementById('subtitles').textContent = '🔄 正在获取字幕，请稍候...';
    document.getElementById('retry').style.display = 'none';
    document.getElementById('copyAll').style.display = 'none';

    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
        const currentTab = tabs[0];

        // 检查是否在YouTube页面
        if (!currentTab.url.includes('youtube.com')) {
            showError('❌ 请在 YouTube 视频页面使用此插件');
            return;
        }

        // 添加超时处理
        const timeout = setTimeout(() => {
            showError('⏰ 获取字幕超时，请重试');
        }, 15000); // 15秒超时

        chrome.tabs.sendMessage(currentTab.id, {action: "getSubtitles"}, function(response) {
            clearTimeout(timeout);

            if (chrome.runtime.lastError) {
                console.error('Chrome runtime error:', chrome.runtime.lastError);
                showError('🔌 插件通信失败，请刷新页面后重试');
                return;
            }

            if (response && response.subtitles) {
                subtitlesData = response;
                updateUI(response);
            } else if (response && response.error) {
                if (response.error.includes('正在获取字幕')) {
                    showError('⏳ 正在处理中，请稍后再试');
                } else {
                    showError('❌ ' + response.error);
                }
            } else {
                showError('❌ 无法获取字幕，请确保视频有可用字幕并重试');
            }
        });
    });
}

function updateUI(data) {
    document.getElementById('language').textContent = '语言: ' + data.language;
    document.getElementById('subtitles').textContent = data.subtitles;
    document.getElementById('retry').style.display = 'none';
    document.getElementById('copyAll').style.display = 'block';
}

function showError(message) {
    document.getElementById('subtitles').textContent = message;
    document.getElementById('retry').style.display = 'inline-block';
    document.getElementById('copyAll').style.display = 'none';
}

document.addEventListener('DOMContentLoaded', function() {
    getSubtitles();

    document.getElementById('copyAll').addEventListener('click', function() {
        if (subtitlesData) {
            const instructionText = "请使用Markdown语法并使用中文，帮我总结关键信息和详细重点内容。你的回应应该以清晰的方式总结原文中的主要信息和重要内容，使用适当的标题、标记和格式，以便易于阅读和理解。\n\n请注意，你的回应应该保留原文中的相关详细信息，同时以简洁明了的方式呈现。你可以自由选择要重点突出的内容，并使用适当的Markdown标记来强调。\n\n---\n\n";
            const textToCopy = instructionText + subtitlesData.subtitles;
            
            navigator.clipboard.writeText(textToCopy).then(function() {
                showToast('字幕已复制到剪贴板！');
            });
        }
    });

    document.getElementById('retry').addEventListener('click', getSubtitles);
});

function showToast(message) {
    const toast = document.getElementById('toast');
    toast.textContent = message;
    toast.classList.add('show');
    setTimeout(() => {
        toast.classList.remove('show');
    }, 3000);
}