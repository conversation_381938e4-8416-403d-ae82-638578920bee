let subtitlesData = null;

function getSubtitles() {
    // 显示加载状态
    document.getElementById('subtitles').textContent = '🔄 正在获取字幕，请稍候...';
    document.getElementById('retry').style.display = 'none';
    document.getElementById('copyAll').style.display = 'none';

    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
        const currentTab = tabs[0];

        // 检查是否在YouTube页面
        if (!currentTab.url.includes('youtube.com')) {
            showError('❌ 请在 YouTube 视频页面使用此插件');
            return;
        }

        // 检查是否在视频页面
        if (!currentTab.url.includes('/watch?v=')) {
            showError('❌ 请在 YouTube 视频播放页面使用此插件');
            return;
        }

        // 添加超时处理 - 增加到25秒
        const timeout = setTimeout(() => {
            showError('⏰ 获取字幕超时，请重试\n\n💡 提示：\n• 确保视频有字幕（点击CC按钮）\n• 尝试刷新页面后重试\n• 某些视频可能没有可用字幕');
        }, 25000); // 25秒超时

        chrome.tabs.sendMessage(currentTab.id, {action: "getSubtitles"}, function(response) {
            clearTimeout(timeout);

            if (chrome.runtime.lastError) {
                console.error('Chrome runtime error:', chrome.runtime.lastError);
                const errorMsg = chrome.runtime.lastError.message;

                if (errorMsg.includes('Receiving end does not exist')) {
                    showError('🔌 插件未正确加载，请刷新页面后重试');
                } else {
                    showError(`🔌 插件通信失败：${errorMsg}\n\n请刷新页面后重试`);
                }
                return;
            }

            if (response && response.subtitles) {
                subtitlesData = response;
                updateUI(response);
            } else if (response && response.error) {
                let errorMessage = response.error;

                // 为常见错误提供更友好的提示
                if (errorMessage.includes('正在获取字幕')) {
                    showError('⏳ 正在处理中，请稍后再试');
                } else if (errorMessage.includes('无法获取视频ID')) {
                    showError('❌ 无法识别当前视频\n\n💡 请确保在YouTube视频播放页面');
                } else if (errorMessage.includes('没有字幕') || errorMessage.includes('no subtitle')) {
                    showError('❌ 该视频没有可用字幕\n\n💡 提示：\n• 尝试其他有字幕的视频\n• 检查视频是否有CC字幕选项');
                } else if (errorMessage.includes('CORS') || errorMessage.includes('网络')) {
                    showError('❌ 网络访问受限\n\n💡 请尝试：\n• 刷新页面后重试\n• 检查网络连接');
                } else {
                    showError('❌ ' + errorMessage);
                }
            } else {
                showError('❌ 无法获取字幕\n\n💡 可能原因：\n• 视频没有字幕\n• 网络连接问题\n• 请刷新页面后重试');
            }
        });
    });
}

function updateUI(data) {
    document.getElementById('language').textContent = '语言: ' + data.language;

    // 检查字幕内容长度并提供反馈
    if (data.subtitles && data.subtitles.trim().length > 0) {
        document.getElementById('subtitles').textContent = data.subtitles;
        document.getElementById('retry').style.display = 'none';
        document.getElementById('copyAll').style.display = 'block';

        // 显示字幕统计信息
        const lines = data.subtitles.split('\n').filter(line => line.trim().length > 0);
        const wordCount = data.subtitles.split(/\s+/).length;
        console.log(`✅ 字幕获取成功: ${lines.length} 行, 约 ${wordCount} 词`);
    } else {
        showError('❌ 获取到的字幕内容为空\n\n💡 请尝试：\n• 刷新页面后重试\n• 确保视频有可用字幕');
    }
}

function showError(message) {
    document.getElementById('subtitles').textContent = message;
    document.getElementById('retry').style.display = 'inline-block';
    document.getElementById('copyAll').style.display = 'none';
}

document.addEventListener('DOMContentLoaded', function() {
    getSubtitles();

    document.getElementById('copyAll').addEventListener('click', function() {
        if (subtitlesData) {
            const instructionText = "请使用Markdown语法并使用中文，帮我总结关键信息和详细重点内容。你的回应应该以清晰的方式总结原文中的主要信息和重要内容，使用适当的标题、标记和格式，以便易于阅读和理解。\n\n请注意，你的回应应该保留原文中的相关详细信息，同时以简洁明了的方式呈现。你可以自由选择要重点突出的内容，并使用适当的Markdown标记来强调。\n\n---\n\n";
            const textToCopy = instructionText + subtitlesData.subtitles;
            
            navigator.clipboard.writeText(textToCopy).then(function() {
                showToast('字幕已复制到剪贴板！');
            });
        }
    });

    document.getElementById('retry').addEventListener('click', getSubtitles);
});

function showToast(message) {
    const toast = document.getElementById('toast');
    toast.textContent = message;
    toast.classList.add('show');
    setTimeout(() => {
        toast.classList.remove('show');
    }, 3000);
}