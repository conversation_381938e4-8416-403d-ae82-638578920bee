# SubSnap 字幕获取修复说明

## 修复内容

### 🔧 主要问题修复

1. **CORS错误修复**
   - 使用background script绕过CORS限制
   - 添加重试机制，提高成功率
   - 优化请求头和参数

2. **网络连接优化**
   - 添加超时处理（8秒超时）
   - 实现自动重试机制（最多3次）
   - 改进错误处理和用户反馈

3. **字幕获取策略优化**
   - 优先使用window.ytInitialPlayerResponse数据
   - 改进页面脚本字幕URL提取
   - 增强实时字幕获取功能
   - 支持多种字幕格式（XML、VTT）

4. **用户体验改进**
   - 更友好的错误信息
   - 增加超时时间（25秒）
   - 提供详细的故障排除提示
   - 添加字幕统计信息

### 🚀 新增功能

1. **多重获取方法**
   - 方法1：简化API调用（通过background script）
   - 方法2：页面脚本提取（从window对象和页面脚本）
   - 方法3：实时获取（激活字幕并收集）

2. **智能重试机制**
   - Background script自动重试
   - 多种URL格式尝试
   - 渐进式等待时间

3. **增强的错误处理**
   - 详细的错误分类
   - 针对性的解决建议
   - 更好的用户反馈

## 使用方法

### 正常使用
1. 在YouTube视频页面打开插件
2. 等待字幕自动获取（最多25秒）
3. 点击"复制全部"按钮复制字幕

### 故障排除

#### 如果遇到"字幕获取失败"
1. **刷新页面**：最简单有效的解决方法
2. **检查字幕**：确保视频有字幕（点击CC按钮）
3. **等待加载**：视频刚打开时可能需要等待
4. **尝试其他视频**：某些视频可能没有可用字幕

#### 如果遇到"插件通信失败"
1. **刷新页面**：重新加载插件
2. **重新安装插件**：如果问题持续存在
3. **检查浏览器版本**：确保使用最新版本Chrome

#### 如果遇到"网络访问受限"
1. **检查网络连接**
2. **禁用其他扩展**：可能存在冲突
3. **清除浏览器缓存**

## 调试工具

### 快速诊断
在YouTube页面的控制台中运行：
```javascript
// 复制 debug-simple.js 的内容到控制台
```

### 详细测试
在YouTube页面的控制台中运行：
```javascript
// 复制 test-subtitle-fix.js 的内容到控制台
```

## 技术细节

### 修复的核心问题
1. **CORS策略阻止**：YouTube API调用被浏览器CORS策略阻止
2. **网络连接不稳定**：ERR_CONNECTION_CLOSED和ERR_ABORTED错误
3. **权限问题**：requestStorageAccessFor权限被拒绝
4. **字幕格式多样性**：需要支持XML和VTT等多种格式

### 解决方案
1. **Background Script代理**：使用service worker绕过CORS
2. **多重获取策略**：实现多种备用方案
3. **智能重试**：自动重试失败的请求
4. **格式兼容**：支持多种字幕格式解析

### 性能优化
1. **缓存机制**：避免重复请求
2. **超时控制**：防止长时间等待
3. **并发限制**：避免同时多个请求
4. **资源清理**：及时清理观察器和定时器

## 版本信息

- **修复版本**：1.2.1
- **修复日期**：2024年12月
- **兼容性**：Chrome 88+, Manifest V3

## 常见问题

**Q: 为什么有时候需要等很久？**
A: 字幕获取需要多个步骤，包括页面数据提取、API调用、实时获取等，每个步骤都有重试机制。

**Q: 为什么某些视频无法获取字幕？**
A: 可能原因：1) 视频没有字幕 2) 字幕被限制访问 3) 网络问题

**Q: 如何提高成功率？**
A: 1) 确保视频完全加载 2) 激活字幕显示 3) 使用有字幕的视频测试

## 反馈和支持

如果遇到问题，请提供：
1. 视频URL
2. 错误信息
3. 浏览器版本
4. 控制台日志（F12 -> Console）
