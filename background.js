// Background script for SubSnap extension
// 处理字幕获取请求，绕过CORS限制

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'fetchSubtitles') {
        fetchSubtitlesInBackground(request.url)
            .then(data => {
                sendResponse({ data: data });
            })
            .catch(error => {
                console.error('Background fetch error:', error);
                sendResponse({ error: error.message });
            });
        
        // 返回true表示异步响应
        return true;
    }
});

async function fetchSubtitlesInBackground(url) {
    try {
        console.log('Background script fetching:', url);

        // 添加重试机制
        let lastError;
        const maxRetries = 3;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                console.log(`Background fetch attempt ${attempt}/${maxRetries}`);

                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'text/xml, application/xml, text/plain, */*',
                        'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
                        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    },
                    signal: AbortSignal.timeout(8000) // 8秒超时
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const subtitlesXml = await response.text();
                console.log('Background fetched XML length:', subtitlesXml.length);

                if (subtitlesXml.length === 0) {
                    throw new Error('Empty response from subtitle server');
                }

                // 检查是否返回了错误页面
                if (subtitlesXml.includes('<html') || subtitlesXml.includes('<!DOCTYPE') ||
                    subtitlesXml.includes('404') || subtitlesXml.includes('error')) {
                    throw new Error('Server returned error page instead of XML');
                }

                // 尝试解析不同格式的字幕
                let parsedSubtitles = null;

                // 方法1: 解析XML格式
                try {
                    parsedSubtitles = parseXMLSubtitles(subtitlesXml);
                    if (parsedSubtitles) {
                        console.log('✅ XML格式解析成功');
                        return parsedSubtitles;
                    }
                } catch (xmlError) {
                    console.log('XML解析失败:', xmlError.message);
                }

                // 方法2: 解析VTT格式
                try {
                    parsedSubtitles = parseVTTSubtitles(subtitlesXml);
                    if (parsedSubtitles) {
                        console.log('✅ VTT格式解析成功');
                        return parsedSubtitles;
                    }
                } catch (vttError) {
                    console.log('VTT解析失败:', vttError.message);
                }

                // 方法3: 直接返回文本内容
                if (subtitlesXml.trim()) {
                    console.log('✅ 返回原始文本内容');
                    return subtitlesXml.trim();
                }

                throw new Error('无法解析字幕内容');

            } catch (error) {
                lastError = error;
                console.log(`Background fetch attempt ${attempt} failed:`, error.message);

                if (attempt < maxRetries) {
                    // 等待后重试
                    await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
                }
            }
        }

        throw lastError || new Error('所有重试都失败了');

    } catch (error) {
        console.error('Background fetchSubtitles error:', error);
        throw error;
    }
}

// 解析XML格式字幕
function parseXMLSubtitles(xmlText) {
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(xmlText, "text/xml");

    // 检查XML解析错误
    const parseError = xmlDoc.querySelector('parsererror');
    if (parseError) {
        throw new Error('XML parsing failed');
    }

    const textElements = xmlDoc.getElementsByTagName('text');
    console.log('Background found text elements:', textElements.length);

    if (textElements.length === 0) {
        // 尝试其他可能的标签
        const altElements = xmlDoc.getElementsByTagName('p') ||
                          xmlDoc.getElementsByTagName('span') ||
                          xmlDoc.getElementsByTagName('s');
        if (altElements.length > 0) {
            const subtitles = Array.from(altElements).map(element => {
                const text = element.textContent || element.innerText || '';
                return decodeHtmlEntities(text);
            }).filter(text => text.trim().length > 0);

            return subtitles.join('\n');
        }

        return null;
    }

    const subtitles = Array.from(textElements).map(element => {
        const text = element.textContent || element.innerText || '';
        return decodeHtmlEntities(text);
    }).filter(text => text.trim().length > 0);

    return subtitles.join('\n');
}

// 解析VTT格式字幕
function parseVTTSubtitles(vttText) {
    if (!vttText.includes('WEBVTT')) {
        return null;
    }

    const lines = vttText.split('\n');
    const subtitles = lines.filter(line => {
        const trimmed = line.trim();
        return trimmed &&
               !trimmed.includes('WEBVTT') &&
               !trimmed.includes('-->') &&
               !trimmed.match(/^\d+$/) &&
               !trimmed.match(/^\d{2}:\d{2}:\d{2}/);
    }).map(line => decodeHtmlEntities(line.trim()));

    return subtitles.join('\n');
}

function decodeHtmlEntities(text) {
    return text.replace(/&amp;/g, '&')
              .replace(/&lt;/g, '<')
              .replace(/&gt;/g, '>')
              .replace(/&quot;/g, '"')
              .replace(/&#39;/g, "'")
              .replace(/&#x27;/g, "'")
              .replace(/&#x2F;/g, '/')
              .replace(/&#x60;/g, '`')
              .replace(/&#x3D;/g, '=');
}
