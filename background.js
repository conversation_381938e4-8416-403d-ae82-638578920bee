// Background script for SubSnap extension
// 处理字幕获取请求，绕过CORS限制

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'fetchSubtitles') {
        fetchSubtitlesInBackground(request.url)
            .then(data => {
                sendResponse({ data: data });
            })
            .catch(error => {
                console.error('Background fetch error:', error);
                sendResponse({ error: error.message });
            });
        
        // 返回true表示异步响应
        return true;
    }
});

async function fetchSubtitlesInBackground(url) {
    try {
        console.log('Background script fetching:', url);
        
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Accept': 'text/xml, application/xml, text/plain, */*',
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const subtitlesXml = await response.text();
        console.log('Background fetched XML length:', subtitlesXml.length);
        
        if (subtitlesXml.length === 0) {
            throw new Error('Empty response from subtitle server');
        }
        
        // 检查是否返回了错误页面
        if (subtitlesXml.includes('<html') || subtitlesXml.includes('<!DOCTYPE')) {
            throw new Error('Server returned HTML page instead of XML');
        }
        
        // 解析XML
        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(subtitlesXml, "text/xml");
        
        // 检查XML解析错误
        const parseError = xmlDoc.querySelector('parsererror');
        if (parseError) {
            throw new Error('XML parsing failed');
        }
        
        const textElements = xmlDoc.getElementsByTagName('text');
        console.log('Background found text elements:', textElements.length);
        
        if (textElements.length === 0) {
            // 尝试其他可能的标签
            const altElements = xmlDoc.getElementsByTagName('p') || 
                              xmlDoc.getElementsByTagName('span') ||
                              xmlDoc.getElementsByTagName('s');
            if (altElements.length > 0) {
                const subtitles = Array.from(altElements).map(element => {
                    const text = element.textContent || element.innerText || '';
                    return decodeHtmlEntities(text);
                }).filter(text => text.trim().length > 0);
                
                return subtitles.join('\n');
            }
            
            throw new Error('No text content found in subtitle file');
        }
        
        const subtitles = Array.from(textElements).map(element => {
            const text = element.textContent || element.innerText || '';
            return decodeHtmlEntities(text);
        }).filter(text => text.trim().length > 0);
        
        return subtitles.join('\n');
        
    } catch (error) {
        console.error('Background fetchSubtitles error:', error);
        throw error;
    }
}

function decodeHtmlEntities(text) {
    return text.replace(/&amp;/g, '&')
              .replace(/&lt;/g, '<')
              .replace(/&gt;/g, '>')
              .replace(/&quot;/g, '"')
              .replace(/&#39;/g, "'")
              .replace(/&#x27;/g, "'")
              .replace(/&#x2F;/g, '/')
              .replace(/&#x60;/g, '`')
              .replace(/&#x3D;/g, '=');
}
