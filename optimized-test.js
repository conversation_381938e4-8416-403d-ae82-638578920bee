// 优化后的测试脚本 - 验证修复效果
console.log('🧪 SubSnap 优化测试开始');

async function testOptimizedVersion() {
    console.log('\n=== 测试优化后的字幕获取 ===');
    
    try {
        // 1. 测试缓存机制
        console.log('\n1️⃣ 测试缓存机制');
        console.log('当前缓存状态:', cachedSubtitles ? '有缓存' : '无缓存');
        console.log('预加载状态:', isPreloading ? '进行中' : '空闲');
        console.log('请求处理状态:', isProcessingRequest ? '处理中' : '空闲');
        
        // 2. 测试视频ID获取
        console.log('\n2️⃣ 测试视频ID获取');
        const videoId = getYouTubeVideoId();
        console.log('视频ID:', videoId || '❌ 获取失败');
        
        if (!videoId) {
            console.log('❌ 无法继续测试，请确保在YouTube视频页面');
            return;
        }
        
        // 3. 测试简化API调用（最主要的方法）
        console.log('\n3️⃣ 测试简化API调用');
        const startTime = Date.now();
        const apiResult = await getSubtitlesFromSimpleAPI(videoId);
        const apiTime = Date.now() - startTime;
        
        if (apiResult) {
            console.log('✅ API调用成功');
            console.log('耗时:', apiTime + 'ms');
            console.log('字幕长度:', apiResult.length);
            console.log('前100字符:', apiResult.substring(0, 100));
        } else {
            console.log('❌ API调用失败');
        }
        
        // 4. 测试完整流程
        console.log('\n4️⃣ 测试完整字幕获取流程');
        const fullStartTime = Date.now();
        const fullResult = await getSubtitlesSimple();
        const fullTime = Date.now() - fullStartTime;
        
        if (fullResult) {
            console.log('✅ 完整流程成功');
            console.log('获取方式:', fullResult.language);
            console.log('总耗时:', fullTime + 'ms');
            console.log('字幕条数:', fullResult.subtitles.split('\n').length);
        } else {
            console.log('❌ 完整流程失败');
        }
        
        // 5. 测试插件通信
        console.log('\n5️⃣ 测试插件通信');
        chrome.runtime.sendMessage({action: "getSubtitles"}, (response) => {
            if (response && response.subtitles) {
                console.log('✅ 插件通信成功');
                console.log('返回方式:', response.language);
                console.log('是否使用缓存:', cachedSubtitles ? '是' : '否');
            } else {
                console.log('❌ 插件通信失败:', response?.error);
            }
        });
        
        // 6. 性能统计
        console.log('\n📊 性能统计');
        console.log('API调用耗时:', apiTime + 'ms');
        console.log('完整流程耗时:', fullTime + 'ms');
        console.log('效率提升:', apiTime < 3000 ? '✅ 良好' : '⚠️ 需优化');
        
    } catch (error) {
        console.error('❌ 测试过程出错:', error);
    }
}

// 测试缓存清理机制
function testCacheClearing() {
    console.log('\n🧹 测试缓存清理机制');
    console.log('清理前缓存:', cachedSubtitles ? '有' : '无');
    
    // 模拟视频切换
    const originalVideoId = currentVideoId;
    checkVideoChange();
    
    console.log('清理后缓存:', cachedSubtitles ? '有' : '无');
    console.log('缓存清理:', cachedSubtitles === null ? '✅ 成功' : '❌ 失败');
}

// 测试错误处理
async function testErrorHandling() {
    console.log('\n🚨 测试错误处理');
    
    try {
        // 测试无效视频ID
        const result = await getSubtitlesFromSimpleAPI('invalid_id');
        console.log('无效ID测试:', result ? '❌ 应该失败' : '✅ 正确处理');
    } catch (error) {
        console.log('错误处理:', '✅ 正确捕获错误');
    }
}

// 运行所有测试
async function runAllTests() {
    await testOptimizedVersion();
    testCacheClearing();
    await testErrorHandling();
    
    console.log('\n🎉 测试完成！');
    console.log('如果看到多个 ✅，说明优化成功');
    console.log('如果有 ❌，请检查对应的功能');
}

// 自动运行测试
runAllTests();

// 导出测试函数
window.optimizedTest = {
    runAllTests,
    testOptimizedVersion,
    testCacheClearing,
    testErrorHandling
};
