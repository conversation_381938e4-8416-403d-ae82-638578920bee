// 测试修复后的字幕获取功能
// 在YouTube页面的控制台中运行此脚本

console.log('=== SubSnap 字幕获取测试 ===');

// 测试视频ID获取
function testVideoId() {
    console.log('\n1. 测试视频ID获取:');
    try {
        const videoId = getYouTubeVideoId();
        if (videoId) {
            console.log('✓ 视频ID获取成功:', videoId);
            return videoId;
        } else {
            console.log('✗ 视频ID获取失败');
            return null;
        }
    } catch (error) {
        console.log('✗ 视频ID获取错误:', error.message);
        return null;
    }
}

// 测试PlayerResponse获取
async function testPlayerResponse(videoId) {
    console.log('\n2. 测试PlayerResponse获取:');
    try {
        const playerResponse = await getPlayerResponse(videoId);
        if (playerResponse && playerResponse.captions) {
            console.log('✓ PlayerResponse获取成功');
            console.log('字幕轨道数量:', playerResponse.captions.playerCaptionsTracklistRenderer?.captionTracks?.length || 0);
            return playerResponse;
        } else {
            console.log('✗ PlayerResponse中没有字幕数据');
            return null;
        }
    } catch (error) {
        console.log('✗ PlayerResponse获取错误:', error.message);
        return null;
    }
}

// 测试字幕URL获取
function testSubtitleUrl(playerResponse) {
    console.log('\n3. 测试字幕URL获取:');
    try {
        const captionTracks = playerResponse.captions?.playerCaptionsTracklistRenderer?.captionTracks;
        if (!captionTracks || captionTracks.length === 0) {
            console.log('✗ 没有可用的字幕轨道');
            return null;
        }

        const track = captionTracks.find(track => track.languageCode === 'zh-CN' || track.languageCode === 'zh') ||
                     captionTracks.find(track => track.languageCode === 'en') ||
                     captionTracks.find(track => track.kind === 'asr') ||
                     captionTracks[0];

        if (track) {
            console.log('✓ 找到字幕轨道:', track.languageCode, track.kind);
            console.log('字幕URL:', track.baseUrl);
            return track.baseUrl;
        } else {
            console.log('✗ 没有合适的字幕轨道');
            return null;
        }
    } catch (error) {
        console.log('✗ 字幕URL获取错误:', error.message);
        return null;
    }
}

// 测试字幕内容获取
async function testSubtitleContent(url) {
    console.log('\n4. 测试字幕内容获取:');
    try {
        console.log('尝试获取字幕内容...');
        
        // 简单的fetch测试
        const response = await fetch(url);
        console.log('响应状态:', response.status, response.statusText);
        
        const content = await response.text();
        console.log('响应内容长度:', content.length);
        console.log('响应内容前200字符:', content.substring(0, 200));
        
        if (content.length === 0) {
            console.log('✗ 字幕内容为空');
            return false;
        }
        
        if (content.includes('<html') || content.includes('<!DOCTYPE')) {
            console.log('✗ 返回的是HTML页面，不是字幕');
            return false;
        }
        
        // 尝试解析XML
        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(content, "text/xml");
        const parseError = xmlDoc.querySelector('parsererror');
        
        if (parseError) {
            console.log('✗ XML解析失败:', parseError.textContent);
            return false;
        }
        
        const textElements = xmlDoc.getElementsByTagName('text');
        console.log('✓ 找到字幕条目数:', textElements.length);
        
        if (textElements.length > 0) {
            console.log('✓ 字幕内容获取成功');
            console.log('前3条字幕:');
            for (let i = 0; i < Math.min(3, textElements.length); i++) {
                console.log(`  ${i + 1}. ${textElements[i].textContent}`);
            }
            return true;
        } else {
            console.log('✗ 没有找到字幕文本元素');
            return false;
        }
        
    } catch (error) {
        console.log('✗ 字幕内容获取错误:', error.message);
        return false;
    }
}

// 运行完整测试
async function runFullTest() {
    console.log('开始完整测试...\n');
    
    const videoId = testVideoId();
    if (!videoId) {
        console.log('\n测试终止：无法获取视频ID');
        return;
    }
    
    const playerResponse = await testPlayerResponse(videoId);
    if (!playerResponse) {
        console.log('\n测试终止：无法获取PlayerResponse');
        return;
    }
    
    const subtitleUrl = testSubtitleUrl(playerResponse);
    if (!subtitleUrl) {
        console.log('\n测试终止：无法获取字幕URL');
        return;
    }
    
    const success = await testSubtitleContent(subtitleUrl);
    
    console.log('\n=== 测试结果 ===');
    if (success) {
        console.log('✓ 所有测试通过！字幕获取功能正常');
    } else {
        console.log('✗ 字幕获取仍有问题，需要进一步调试');
    }
}

// 自动运行测试
runFullTest().catch(error => {
    console.error('测试运行错误:', error);
});

// 导出测试函数供手动调用
window.subSnapTest = {
    runFullTest,
    testVideoId,
    testPlayerResponse,
    testSubtitleUrl,
    testSubtitleContent
};
