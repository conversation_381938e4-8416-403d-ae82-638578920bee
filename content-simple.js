// 简化版本的字幕获取 - 专门解决当前问题
let cachedSubtitles = null;

// 主要的字幕获取函数
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === "getSubtitles") {
        getSubtitlesSimple().then(function(result) {
            sendResponse(result);
        }).catch(function(error) {
            sendResponse({error: error.message});
        });
        return true; // 保持消息通道开放
    }
});

async function getSubtitlesSimple() {
    try {
        console.log('开始简化字幕获取流程');
        
        // 1. 获取视频ID
        const videoId = getVideoId();
        if (!videoId) {
            throw new Error('无法获取视频ID');
        }
        console.log('视频ID:', videoId);
        
        // 2. 尝试多种方法获取字幕
        
        // 方法1: 直接从页面获取字幕数据
        const pageSubtitles = await getSubtitlesFromPageDirect();
        if (pageSubtitles) {
            return {
                language: '页面提取',
                subtitles: pageSubtitles
            };
        }
        
        // 方法2: 使用简化的API调用
        const apiSubtitles = await getSubtitlesFromSimpleAPI(videoId);
        if (apiSubtitles) {
            return {
                language: 'API获取',
                subtitles: apiSubtitles
            };
        }
        
        // 方法3: 激活字幕并实时获取
        const liveSubtitles = await getLiveSubtitles();
        if (liveSubtitles) {
            return {
                language: '实时获取',
                subtitles: liveSubtitles
            };
        }
        
        throw new Error('所有方法都无法获取字幕');
        
    } catch (error) {
        console.error('字幕获取失败:', error);
        throw error;
    }
}

// 获取视频ID
function getVideoId() {
    const urlParams = new URLSearchParams(window.location.search);
    const videoId = urlParams.get('v');
    if (videoId) return videoId;
    
    const match = window.location.href.match(/(?:watch\?v=|embed\/|v\/)([a-zA-Z0-9_-]{11})/);
    return match ? match[1] : null;
}

// 方法1: 直接从页面获取字幕数据
async function getSubtitlesFromPageDirect() {
    try {
        console.log('尝试从页面直接获取字幕');
        
        // 查找页面中的字幕数据
        const scripts = document.querySelectorAll('script');
        for (const script of scripts) {
            const content = script.textContent || script.innerHTML;
            
            // 查找字幕相关的JSON数据
            if (content.includes('captionTracks') && content.includes('baseUrl')) {
                console.log('找到包含字幕数据的脚本');
                
                // 尝试提取字幕URL
                const urlMatch = content.match(/"baseUrl":"([^"]*timedtext[^"]*)"/);
                if (urlMatch) {
                    const url = urlMatch[1].replace(/\\u0026/g, '&').replace(/\\/g, '');
                    console.log('提取到字幕URL:', url);
                    
                    try {
                        const response = await fetch(url);
                        const text = await response.text();
                        if (text && text.length > 0) {
                            return parseSubtitleContent(text);
                        }
                    } catch (e) {
                        console.log('字幕URL请求失败:', e.message);
                    }
                }
            }
        }
        
        return null;
    } catch (error) {
        console.error('页面直接获取失败:', error);
        return null;
    }
}

// 方法2: 使用简化的API调用
async function getSubtitlesFromSimpleAPI(videoId) {
    try {
        console.log('尝试简化API调用');
        
        // 尝试不同的API格式
        const formats = ['srv3', 'vtt', 'srv1'];
        const languages = ['en', 'zh-CN', 'zh'];
        
        for (const lang of languages) {
            for (const fmt of formats) {
                const url = `https://www.youtube.com/api/timedtext?v=${videoId}&lang=${lang}&fmt=${fmt}`;
                
                try {
                    console.log('尝试URL:', url);
                    const response = await fetch(url, {
                        method: 'GET',
                        mode: 'cors',
                        credentials: 'omit'
                    });
                    
                    if (response.ok) {
                        const text = await response.text();
                        if (text && text.length > 0 && !text.includes('<html')) {
                            console.log('简化API成功:', url);
                            return parseSubtitleContent(text);
                        }
                    }
                } catch (e) {
                    console.log('API调用失败:', url, e.message);
                }
            }
        }
        
        return null;
    } catch (error) {
        console.error('简化API调用失败:', error);
        return null;
    }
}

// 方法3: 激活字幕并实时获取
async function getLiveSubtitles() {
    try {
        console.log('尝试实时获取字幕');
        
        // 查找字幕按钮
        const captionButton = document.querySelector('.ytp-subtitles-button, .ytp-caption-window-button');
        if (!captionButton) {
            console.log('未找到字幕按钮');
            return null;
        }
        
        // 检查字幕是否已经开启
        const isActive = captionButton.getAttribute('aria-pressed') === 'true';
        if (!isActive) {
            console.log('激活字幕');
            captionButton.click();
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        // 查找字幕容器
        const captionContainer = document.querySelector('.ytp-caption-window-container');
        if (!captionContainer) {
            console.log('未找到字幕容器');
            return null;
        }
        
        // 收集字幕文本
        const subtitles = [];
        const collectSubtitles = () => {
            const spans = captionContainer.querySelectorAll('.ytp-caption-segment');
            spans.forEach(span => {
                const text = span.textContent.trim();
                if (text && !subtitles.includes(text)) {
                    subtitles.push(text);
                }
            });
        };
        
        // 监听字幕变化
        const observer = new MutationObserver(collectSubtitles);
        observer.observe(captionContainer, { childList: true, subtree: true });
        
        // 播放视频一小段时间来获取字幕
        const video = document.querySelector('video');
        if (video) {
            const wasPlaying = !video.paused;
            const currentTime = video.currentTime;
            
            if (video.paused) {
                video.play();
            }
            
            // 等待收集字幕
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            // 恢复原始状态
            if (!wasPlaying) {
                video.pause();
            }
            video.currentTime = currentTime;
        }
        
        observer.disconnect();
        
        if (subtitles.length > 0) {
            console.log('实时获取成功，字幕条数:', subtitles.length);
            return subtitles.join('\n');
        }
        
        return null;
    } catch (error) {
        console.error('实时获取失败:', error);
        return null;
    }
}

// 解析字幕内容
function parseSubtitleContent(content) {
    try {
        // 尝试XML解析
        if (content.includes('<?xml') || content.includes('<transcript>') || content.includes('<text')) {
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(content, "text/xml");
            const textElements = xmlDoc.getElementsByTagName('text');
            
            if (textElements.length > 0) {
                const subtitles = Array.from(textElements).map(element => {
                    return (element.textContent || '').trim();
                }).filter(text => text.length > 0);
                
                return subtitles.join('\n');
            }
        }
        
        // 尝试VTT解析
        if (content.includes('WEBVTT')) {
            const lines = content.split('\n');
            const subtitles = lines.filter(line => {
                const trimmed = line.trim();
                return trimmed && 
                       !trimmed.includes('WEBVTT') && 
                       !trimmed.includes('-->') && 
                       !trimmed.match(/^\d+$/) &&
                       !trimmed.match(/^\d{2}:\d{2}:\d{2}/);
            });
            
            return subtitles.join('\n');
        }
        
        // 如果都不是，直接返回内容
        return content;
        
    } catch (error) {
        console.error('解析字幕内容失败:', error);
        return content;
    }
}
