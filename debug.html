<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SubSnap 调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .debug-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #3367d6;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>SubSnap 插件调试页面</h1>
        <p>此页面用于调试 SubSnap 插件的字幕获取功能。请在 YouTube 视频页面打开此页面。</p>
        
        <div class="debug-section">
            <h3>1. 环境检查</h3>
            <div id="env-status"></div>
            <button onclick="checkEnvironment()">检查环境</button>
        </div>
        
        <div class="debug-section">
            <h3>2. 视频ID获取</h3>
            <div id="video-id-status"></div>
            <button onclick="testVideoId()">测试视频ID获取</button>
        </div>
        
        <div class="debug-section">
            <h3>3. PlayerResponse获取</h3>
            <div id="player-response-status"></div>
            <button onclick="testPlayerResponse()">测试PlayerResponse</button>
        </div>
        
        <div class="debug-section">
            <h3>4. 字幕获取</h3>
            <div id="subtitles-status"></div>
            <button onclick="testSubtitles()">测试字幕获取</button>
        </div>
        
        <div class="debug-section">
            <h3>5. 控制台日志</h3>
            <div id="console-logs"></div>
            <button onclick="clearLogs()">清除日志</button>
        </div>
    </div>

    <script>
        let logs = [];
        
        // 拦截console.log
        const originalLog = console.log;
        const originalError = console.error;
        
        console.log = function(...args) {
            logs.push({type: 'log', message: args.join(' '), time: new Date().toLocaleTimeString()});
            updateConsoleLogs();
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            logs.push({type: 'error', message: args.join(' '), time: new Date().toLocaleTimeString()});
            updateConsoleLogs();
            originalError.apply(console, args);
        };
        
        function updateConsoleLogs() {
            const container = document.getElementById('console-logs');
            container.innerHTML = logs.slice(-10).map(log => 
                `<div class="status ${log.type === 'error' ? 'error' : 'info'}">
                    [${log.time}] ${log.message}
                </div>`
            ).join('');
        }
        
        function clearLogs() {
            logs = [];
            updateConsoleLogs();
        }
        
        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function checkEnvironment() {
            const checks = [];
            
            // 检查是否在YouTube页面
            if (window.location.hostname.includes('youtube.com')) {
                checks.push('✓ 在YouTube页面');
            } else {
                checks.push('✗ 不在YouTube页面');
            }
            
            // 检查是否有视频
            if (window.location.search.includes('v=')) {
                checks.push('✓ URL包含视频ID');
            } else {
                checks.push('✗ URL不包含视频ID');
            }
            
            // 检查window对象
            if (window.ytInitialPlayerResponse) {
                checks.push('✓ 找到ytInitialPlayerResponse');
            } else {
                checks.push('✗ 未找到ytInitialPlayerResponse');
            }
            
            showStatus('env-status', checks.join('<br>'), 'info');
        }
        
        async function testVideoId() {
            try {
                // 这里需要引入content.js中的函数
                const videoId = getYouTubeVideoId();
                if (videoId) {
                    showStatus('video-id-status', `✓ 视频ID: ${videoId}`, 'success');
                } else {
                    showStatus('video-id-status', '✗ 无法获取视频ID', 'error');
                }
            } catch (error) {
                showStatus('video-id-status', `✗ 错误: ${error.message}`, 'error');
            }
        }
        
        async function testPlayerResponse() {
            try {
                const videoId = getYouTubeVideoId();
                if (!videoId) {
                    showStatus('player-response-status', '✗ 需要先获取视频ID', 'error');
                    return;
                }
                
                const playerResponse = await getPlayerResponse(videoId);
                if (playerResponse) {
                    showStatus('player-response-status', '✓ PlayerResponse获取成功', 'success');
                } else {
                    showStatus('player-response-status', '✗ PlayerResponse获取失败', 'error');
                }
            } catch (error) {
                showStatus('player-response-status', `✗ 错误: ${error.message}`, 'error');
            }
        }
        
        async function testSubtitles() {
            try {
                const result = await getYouTubeSubtitles();
                if (result && result.subtitles) {
                    showStatus('subtitles-status', 
                        `✓ 字幕获取成功<br>语言: ${result.language}<br>长度: ${result.subtitles.length} 字符`, 
                        'success');
                } else {
                    showStatus('subtitles-status', '✗ 字幕获取失败', 'error');
                }
            } catch (error) {
                showStatus('subtitles-status', `✗ 错误: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动检查环境
        window.addEventListener('load', checkEnvironment);
    </script>
</body>
</html>
