body {
    font-family: 'Roboto', sans-serif;
    width: 350px;
    height: 450px; /* 设置固定高度 */
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
    color: #333;
    display: flex;
    flex-direction: column;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

header {
    background-color: #4285f4;
    color: white;
    padding: 15px;
    text-align: center;
    flex-shrink: 0; /* 防止header缩小 */
}

h1 {
    margin: 0;
    font-size: 24px;
    font-weight: 500;
}

#language {
    margin: 5px 0 0;
    font-size: 14px;
    opacity: 0.8;
}

main {
    flex-grow: 1;
    overflow-y: auto;
    padding: 15px;
    display: flex;
    flex-direction: column;
}

.subtitles-container {
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    font-size: 14px;
    line-height: 1.5;
    flex-grow: 1;
    overflow-y: auto;
    margin-bottom: 15px; /* 添加底部间距 */
}

footer {
    padding: 15px;
    display: flex;
    justify-content: space-between;
    flex-shrink: 0; /* 防止footer缩小 */
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.btn-primary {
    background-color: #4285f4;
    color: white;
}

.btn-primary:hover {
    background-color: #3367d6;
}

.btn-secondary {
    background-color: #f1f3f4;
    color: #5f6368;
}

.btn-secondary:hover {
    background-color: #e8eaed;
}

.toast {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #323232;
    color: white;
    padding: 10px 20px;
    border-radius: 4px;
    font-size: 14px;
    opacity: 0;
    transition: opacity 0.3s;
}

.toast.show {
    opacity: 1;
}